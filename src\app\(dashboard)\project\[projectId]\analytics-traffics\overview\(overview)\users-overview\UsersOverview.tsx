"use client";
import React, { useState } from "react";

/* =============================== COMPONENTS =============================== */
import Card from "@/components/ui/card";
import Title from "@/components/ui/Title";
import ChoroplethMap from "./_components/ChoroplethMap";
import DateRange from "../../../_components/date-range/DateRange";
import ProgressBar from "./_components/ProgressBar";
import Badge from "../../../analytic-insight/_components/Badge";
import { Button } from "@/components/ui/button";

/* ================================ API CALLS =============================== */
import useUsersOverview from "./UsersOverview.hook";
import { useAppThemeColor } from "@/hooks/useSidebarThemeColor";
import Link from "next/link";

/* ========================================================================== */
const UsersOverview = () => {
  /* ========================================================================== */
  /*                                  CONSTANTS                                 */
  /* ========================================================================== */
  const { themeColor } = useAppThemeColor();
  const [selectedItem, setSelectedItem] = useState("Countries");
  const {
    data: useUsersData,
    isLoading: useUsersIsLoading,
    error,
  } = useUsersOverview(selectedItem);

  const badges = ["Countries", "Cities", "Gender", "Device", "Age"];

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */
  // Show error state if there's an error
  if (error) {
    return (
      <Card className="overflow-hidden space-y-4">
        <Title>Users Overview</Title>
        <DateRange />
        <div className="flex items-center justify-center h-64 text-red-500">
          <p>Error loading users overview data. Please try again.</p>
        </div>
      </Card>
    );
  }

  return (
    <Card className="overflow-hidden space-y-4">
      <div className="flex items-center justify-between">
        <Title>Users Overview</Title>
        <div className="text-sm text-gray-500">
          Active: {selectedItem}
          {useUsersData && (
            <span className="ml-2 text-green-600">
              ✓ Data Loaded (
              {Object.keys(useUsersData.leftMap).length +
                Object.keys(useUsersData.rightMap).length}{" "}
              countries)
            </span>
          )}
        </div>
      </div>
      <DateRange />
      {/* Debug Panel */}
      {useUsersData && (
        <div className="bg-gray-50 p-4 rounded-lg mb-4">
          <h3 className="font-semibold text-sm mb-2">
            🔍 Debug Info for Tab: {selectedItem}
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-xs">
            <div>
              <h4 className="font-medium text-purple-600 mb-1">
                Left Map Data ({Object.keys(useUsersData.leftMap).length}{" "}
                countries):
              </h4>
              <div className="bg-white p-2 rounded border max-h-32 overflow-y-auto">
                {Object.entries(useUsersData.leftMap).map(([code, value]) => {
                  const [percentage, users, _percent] = value.split(",");
                  return (
                    <div key={code} className="flex justify-between">
                      <span className="font-mono">{code}:</span>
                      <span>
                        {percentage}% ({users} users)
                      </span>
                    </div>
                  );
                })}
              </div>
            </div>
            <div>
              <h4 className="font-medium text-yellow-600 mb-1">
                Right Map Data ({Object.keys(useUsersData.rightMap).length}{" "}
                countries):
              </h4>
              <div className="bg-white p-2 rounded border max-h-32 overflow-y-auto">
                {Object.entries(useUsersData.rightMap).map(([code, value]) => {
                  const [percentage, users, _percent] = value.split(",");
                  return (
                    <div key={code} className="flex justify-between">
                      <span className="font-mono">{code}:</span>
                      <span>
                        {percentage}% ({users} users)
                      </span>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
          <div className="mt-3">
            <h4 className="font-medium text-green-600 mb-1">
              Progress Bar Data ({useUsersData.progressbarData.length} items):
            </h4>
            <div className="bg-white p-2 rounded border max-h-24 overflow-y-auto">
              <div className="flex flex-wrap gap-2">
                {useUsersData.progressbarData.map((item, index) => (
                  <span
                    key={index}
                    className="bg-gray-100 px-2 py-1 rounded text-xs"
                  >
                    {item.title}: {item.percentage}%
                  </span>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="flex flex-col lg:flex-row gap-2 lg:items-start mt-8">
        <div className="w-[100%] lg:w-[50%] h-fit md:h-[900px] lg:h-fit grid grid-cols-1 lg:grid-cols-1">
          {useUsersIsLoading && !useUsersData && (
            <>
              <ChoroplethMap
                color="#914AC4"
                countryValues={{ IRN: "0" }}
                className="animate-pulse"
              />
              <ChoroplethMap
                color="#F8BD00"
                countryValues={{ IRN: "0" }}
                className="animate-pulse"
              />
            </>
          )}
          {useUsersData && (
            <>
              {/* ALL TABS USE THE SAME MAP DATA (countries data) */}
              {/* Only the progress bar data on the right changes based on selected tab */}
              <div className="">
                <ChoroplethMap
                  color="#914AC4"
                  countryValues={useUsersData?.leftMap || { IRN: "0" }}
                />
              </div>
              <div className="">
                <ChoroplethMap
                  color="#F8BD00"
                  countryValues={useUsersData?.rightMap || { IRN: "0" }}
                />
              </div>
            </>
          )}
        </div>
        <div className="w-full lg:w-[50%] space-y-6 p-4">
          <div className="flex gap-2.5 justify-between overflow-x-auto">
            {badges.map((badge, index) => (
              <Badge
                onSelect={() => setSelectedItem(badge)}
                key={index}
                className={`px-3`}
                style={
                  selectedItem === badge
                    ? {
                        backgroundColor: themeColor + "10",
                        color: themeColor,
                        borderColor: "transparent",
                      }
                    : {}
                }
              >
                {badge}
              </Badge>
            ))}
          </div>
          {useUsersData
            ? useUsersData.progressbarData.map(
                ({ title, percentage }, index) => (
                  <ProgressBar
                    key={index}
                    isLoading={false}
                    percentage={percentage}
                    title={title}
                    color={index === 0 ? "bg-[#F8BD00]" : ""}
                  />
                )
              )
            : useUsersIsLoading &&
              Array.from({ length: 7 }).map((_, index) => (
                <ProgressBar
                  key={index}
                  isLoading={true}
                  percentage={Math.floor(Math.random() * 101)}
                  title={"loading..."}
                  color={index === 0 ? "bg-[#F8BD00]" : ""}
                />
              ))}
        </div>
      </div>
      <div className="w-full flex justify-end py-5 px-3">
        <Link href={"/project/analytics-traffics/analytic-insight?tab=users"}>
          <Button className="justify-self-end" variant={"default"}>
            See All Details
          </Button>
        </Link>
      </div>
    </Card>
  );
};

export default UsersOverview;
