import AXIOS from "@/lib/axios";
import { useQuery } from "@tanstack/react-query";
import type { TrafficOverviewResponse } from "../../../types/HorizontalBars.types";
import { useProjectId } from "@/hooks/useProjectId";

const useTrafficOverview = () => {
  const { projectId, isValidProjectId } = useProjectId();

  return useQuery({
    queryKey: ["traffic-overview-data", projectId],
    queryFn: async (): Promise<TrafficOverviewResponse> => {
      if (!projectId) {
        throw new Error("Project ID is required");
      }

      const { data } = await AXIOS.get(
        "/api/dashboard/project/analytics-traffics/overview/traffic-overview",
        {
          params: {
            projectId,
          },
        }
      );
      return data;
    },
    enabled: isValidProjectId && !!projectId,
  });
};

export default useTrafficOverview;
