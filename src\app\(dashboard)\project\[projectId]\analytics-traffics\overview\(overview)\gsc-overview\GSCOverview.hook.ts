import { useQuery } from "@tanstack/react-query";
import AXIOS from "@/lib/axios";
import { GSCOverviewType, GSCAPIResponse } from "./GSCOverview.types";
import { useProjectId } from "@/hooks/useProjectId";
import { useDateRangeStore } from "@/store/useDateRangeStore";
import { processGSCData } from "./GSCOverview.utils";

export const useGSCOverview = () => {
  const { projectId, isValidProjectId } = useProjectId();
  const { getFormattedDates, isComparisonEnabled } = useDateRangeStore();

  return useQuery({
    queryKey: [
      "gsc-overview-data",
      projectId,
      getFormattedDates(),
      isComparisonEnabled,
    ],
    queryFn: async (): Promise<GSCOverviewType> => {
      if (!projectId) {
        throw new Error("Project ID is required");
      }

      const { startDate, endDate, comparisonStartDate, comparisonEndDate } =
        getFormattedDates();

      // Primary date range API call
      const primaryResponse = await AXIOS.get<GSCAPIResponse>(
        "/api/dashboard/project/analytics-traffics/overview/gsc-overview",
        {
          params: {
            projectId,
            start_date: startDate,
            end_date: endDate,
          },
        }
      );

      let comparisonResponse: { data: GSCAPIResponse } | null = null;

      // Comparison date range API call (if comparison is enabled and dates are available)
      if (isComparisonEnabled && comparisonStartDate && comparisonEndDate) {
        comparisonResponse = await AXIOS.get<GSCAPIResponse>(
          "/api/dashboard/project/analytics-traffics/overview/gsc-overview",
          {
            params: {
              projectId,
              start_date: comparisonStartDate,
              end_date: comparisonEndDate,
            },
          }
        );
      }

      // Process and merge the data
      return processGSCData(primaryResponse.data, comparisonResponse?.data);
    },
    enabled: isValidProjectId && !!projectId,
  });
};
