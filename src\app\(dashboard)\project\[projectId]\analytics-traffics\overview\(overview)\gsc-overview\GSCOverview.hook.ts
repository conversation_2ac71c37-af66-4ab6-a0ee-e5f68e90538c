import { useQuery } from "@tanstack/react-query";
import AXIOS from "@/lib/axios";
import { GSCOverviewType, GSCAPIResponse } from "./GSCOverview.types";
import { useProjectId } from "@/hooks/useProjectId";
import { useDateRangeStore } from "@/store/useDateRangeStore";
import { processGSCData } from "./GSCOverview.utils";
import {
  MOCK_PRIMARY_DATA,
  MOCK_COMPARISON_DATA,
} from "./GSCOverview.mockData";

export const useGSCOverview = () => {
  const { projectId, isValidProjectId } = useProjectId();
  const { getFormattedDates, isComparisonEnabled } = useDateRangeStore();

  return useQuery({
    queryKey: [
      "gsc-overview-data",
      projectId,
      getFormattedDates(),
      isComparisonEnabled,
    ],
    queryFn: async (): Promise<GSCOverviewType> => {
      if (!projectId) {
        throw new Error("Project ID is required");
      }

      // 🧪 TESTING: Using mock data instead of API calls
      // TODO: Replace with actual API calls when ready for production
      // const primaryResponse = await AXIOS.get<GSCAPIResponse>("/api/dashboard/project/analytics-traffics/overview/gsc-overview", { params: { projectId, start_date: startDate, end_date: endDate } });
      console.log("🔄 Using mock data for GSC Overview testing");

      // Simulate API delay for realistic testing
      await new Promise((resolve) => setTimeout(resolve, 500));

      const { startDate, endDate, comparisonStartDate, comparisonEndDate } =
        getFormattedDates();

      console.log("📅 Date ranges:", {
        primary: { startDate, endDate },
        comparison: { comparisonStartDate, comparisonEndDate },
        isComparisonEnabled,
      });

      // Use mock primary data
      const primaryResponse = { data: MOCK_PRIMARY_DATA };

      let comparisonResponse: { data: GSCAPIResponse } | null = null;

      // Use mock comparison data if comparison is enabled
      if (isComparisonEnabled && comparisonStartDate && comparisonEndDate) {
        console.log("📊 Including comparison data");
        comparisonResponse = { data: MOCK_COMPARISON_DATA };
      } else {
        console.log("📊 Primary data only");
      }

      // Process and merge the data
      const result = processGSCData(
        primaryResponse.data,
        comparisonResponse?.data
      );
      console.log("✅ Processed GSC data:", {
        lineChartDataLength: result.lineChartData.length,
        cardsDataKeys: Object.keys(result.cardsData),
        hasComparisonData: !!comparisonResponse,
      });

      return result;
    },
    enabled: isValidProjectId && !!projectId,
  });
};
