// GSC API Response Types
export type GSCDailyMetric = {
  date: string; // Format: "20250601"
  Clicks: string;
  CTR: string;
  Search_Visibility: string;
  Impression: string;
  Avg_Position: string;
};

export type GSCTotals = {
  Clicks: string;
  CTR: string;
  Search_Visibility: string;
  Impression: string;
  Avg_Position: string;
};

export type GSCPeriod = {
  start_date: string; // Format: "2025-06-01"
  end_date: string; // Format: "2025-06-30"
  days_count: number;
};

export type GSCWebPagesData = {
  totals: GSCTotals;
  daily_metrics: GSCDailyMetric[];
};

export type GSCAPIResponse = {
  status: string;
  project_id: string;
  period: GSCPeriod;
  data: {
    "web-pages": GSCWebPagesData;
  };
};

// Processed data types for chart consumption
export type GSCOverviewType = {
  lineChartData: Record<string, string | number>[];
  cardsData: Record<string, CardsData>;
};

export type CardsData = { amount: number; growth: string };

export type CheckboxCardProps = {
  title: string;
  color: string;
  cardsData: CardsData;
  selected: string[];
  onToggleCheck: () => void;
};
