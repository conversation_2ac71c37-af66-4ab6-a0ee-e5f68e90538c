export type BarsType = {
  label: string;
  barData: BarData[];
};
type BarData = {
  value: number;
  color: string;
};
export type BarsData = {
  maxValue: number;
  bars: BarsType[];
};

// New types for the traffic overview API response
export interface TrafficSource {
  value: number;
  percentage: number;
}

export interface TrafficSources {
  organic: TrafficSource;
  paid: TrafficSource;
  direct: TrafficSource;
  social: TrafficSource;
  referral: TrafficSource;
  email: TrafficSource;
  unassigned: TrafficSource;
}

export interface Metric {
  total_value: number;
  traffic_sources: TrafficSources;
}

export interface Metrics {
  total_users: Metric;
  new_users: Metric;
  sessions: Metric;
  active_users: Metric;
  page_views: Metric;
  event_count: Metric;
  conversions: Metric;
}

export interface Period {
  start_date: string;
  end_date: string;
  days_count: number;
}

export interface TrafficOverviewData {
  period: Period;
  metrics: Metrics;
}

export interface TrafficOverviewResponse {
  status: string;
  project_id: string;
  data: TrafficOverviewData;
  last_sync: string;
}

// Extended types for comparison data
export interface ComparisonTrafficOverviewData {
  current: TrafficOverviewData;
  previous: TrafficOverviewData;
}

export interface ComparisonTrafficOverviewResponse {
  status: string;
  project_id: string;
  data: ComparisonTrafficOverviewData;
  last_sync: string;
}
