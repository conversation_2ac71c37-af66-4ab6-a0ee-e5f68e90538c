import type {
  TrafficOverviewData,
  BarsData,
  Metric,
  ComparisonTrafficOverviewData,
} from "../../../../types/HorizontalBars.types";

// Traffic source labels for display
const TRAFFIC_SOURCE_LABELS = {
  organic: "Organic",
  paid: "Paid",
  direct: "Direct",
  social: "Social",
  referral: "Referral",
  email: "Email",
  unassigned: "Unassigned",
} as const;

/**
 * Transform comparison traffic overview data for a specific metric into chart format
 * Shows previous period in yellow (#FFCD29) and current period in purple
 */
export const transformComparisonMetricToChartData = (
  currentMetric: Metric,
  previousMetric: Metric,
  metricName: string
): BarsData => {
  // Get all traffic sources that have data in either period
  const allSources = new Set([
    ...Object.keys(currentMetric.traffic_sources),
    ...Object.keys(previousMetric.traffic_sources),
  ]);

  const trafficSourceBars = Array.from(allSources)
    .filter((key) => {
      const currentValue =
        currentMetric.traffic_sources[
          key as keyof typeof currentMetric.traffic_sources
        ]?.value || 0;
      const previousValue =
        previousMetric.traffic_sources[
          key as keyof typeof previousMetric.traffic_sources
        ]?.value || 0;
      return currentValue > 0 || previousValue > 0;
    })
    .map((key) => {
      const currentValue =
        currentMetric.traffic_sources[
          key as keyof typeof currentMetric.traffic_sources
        ]?.value || 0;
      const previousValue =
        previousMetric.traffic_sources[
          key as keyof typeof previousMetric.traffic_sources
        ]?.value || 0;

      const sourceName =
        TRAFFIC_SOURCE_LABELS[key as keyof typeof TRAFFIC_SOURCE_LABELS];

      const barData = [];

      // Case 1: Growth (current > previous)
      if (currentValue > previousValue && previousValue > 0) {
        // Show previous period value in yellow
        barData.push({
          value: previousValue,
          color: "bg-[#FFCD29]",
          actualValue: previousValue,
        });
        // Show current period value in purple (but visually only the difference)
        barData.push({
          value: currentValue - previousValue,
          color: "bg-purple-500",
          actualValue: currentValue,
        });
      }
      // Case 2: Decline (current < previous)
      else if (currentValue < previousValue && currentValue > 0) {
        // Show current period value in purple
        barData.push({
          value: currentValue,
          color: "bg-purple-500",
          actualValue: currentValue,
        });
        barData.push({
          value: previousValue - currentValue,
          color: "bg-yellow-200",
          actualValue: previousValue,
        });
      }
      // Case 3: Only current value exists (no previous)
      else if (currentValue > 0 && previousValue === 0) {
        barData.push({
          value: currentValue,
          color: "bg-purple-500",
          actualValue: currentValue,
        });
      }
      // Case 4: Only previous value exists (no current)
      else if (previousValue > 0 && currentValue === 0) {
        barData.push({
          value: previousValue,
          color: "bg-[#FFCD29]",
          actualValue: previousValue,
        });
      }
      // Case 5: Same values
      else if (currentValue === previousValue && currentValue > 0) {
        barData.push({
          value: currentValue,
          color: "bg-purple-500",
          actualValue: currentValue,
        });
      }

      return {
        label: sourceName,
        barData,
      };
    });

  // If no traffic sources have data, show a placeholder
  const finalBars =
    trafficSourceBars.length > 0
      ? trafficSourceBars
      : [
          {
            label: "No Data",
            barData: [{ value: 0, color: "bg-gray-300" }],
          },
        ];

  // Use the maximum total value from both periods for proper scaling
  const maxValue = Math.max(
    currentMetric.total_value,
    previousMetric.total_value,
    1
  );

  return {
    maxValue,
    bars: finalBars,
  };
};

/**
 * Get metric data by metric name
 */
export const getMetricByName = (
  data: TrafficOverviewData,
  metricName: string
): Metric | null => {
  const metricMap: Record<string, keyof TrafficOverviewData["metrics"]> = {
    "Total Users": "total_users",
    "New Users": "new_users",
    Sessions: "sessions",
    "Active Users": "active_users",
    Views: "page_views",
    "Event Count": "event_count",
    Conversions: "conversions",
  };

  const metricKey = metricMap[metricName];
  return metricKey ? data.metrics[metricKey] : null;
};

/**
 * Format number for display (e.g., 1000 -> 1K)
 */
export const formatMetricValue = (value: number): string => {
  if (value >= 1000000) {
    return `${(value / 1000000).toFixed(1)}M`;
  }
  if (value >= 1000) {
    return `${(value / 1000).toFixed(1)}K`;
  }
  return value.toString();
};
