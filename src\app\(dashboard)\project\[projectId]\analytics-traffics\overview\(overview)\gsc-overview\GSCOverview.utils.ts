import { GSCAPIResponse, GSCOverviewType, CardsData } from "./GSCOverview.types";

/**
 * Formats date from API format (YYYYMMDD) to display format (MMM DD)
 */
const formatDateForChart = (dateString: string): string => {
  const year = dateString.substring(0, 4);
  const month = dateString.substring(4, 6);
  const day = dateString.substring(6, 8);
  
  const date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
  return date.toLocaleDateString("en-US", {
    month: "short",
    day: "numeric",
  });
};

/**
 * Calculates growth percentage between two values
 */
const calculateGrowth = (current: number, previous: number): string => {
  if (previous === 0) return current > 0 ? "+100%" : "0%";
  const growth = ((current - previous) / previous) * 100;
  return growth >= 0 ? `+${growth.toFixed(1)}%` : `${growth.toFixed(1)}%`;
};

/**
 * Converts string values to numbers for calculations
 */
const parseMetricValue = (value: string): number => {
  // Handle values like "1498k" or plain numbers
  if (value.includes("k")) {
    return parseFloat(value.replace("k", "")) * 1000;
  }
  return parseFloat(value) || 0;
};

/**
 * Processes GSC API responses into chart-compatible format
 */
export const processGSCData = (
  primaryData: GSCAPIResponse,
  comparisonData?: GSCAPIResponse
): GSCOverviewType => {
  const metrics = ["Clicks", "CTR", "Search_Visibility", "Impression", "Avg_Position"];
  
  // Process primary data
  const primaryMetrics = primaryData.data["web-pages"].daily_metrics;
  const primaryTotals = primaryData.data["web-pages"].totals;
  
  // Process comparison data if available
  const comparisonMetrics = comparisonData?.data["web-pages"].daily_metrics || [];
  const comparisonTotals = comparisonData?.data["web-pages"].totals;
  
  // Create line chart data
  const lineChartData: Record<string, string | number>[] = [];
  
  // Process primary data points
  primaryMetrics.forEach((metric) => {
    const chartPoint: Record<string, string | number> = {
      name: formatDateForChart(metric.date),
    };
    
    // Add primary metrics (normal lines)
    metrics.forEach((metricName) => {
      chartPoint[metricName] = parseMetricValue(metric[metricName as keyof typeof metric]);
    });
    
    lineChartData.push(chartPoint);
  });
  
  // Add comparison data if available (dotted lines)
  if (comparisonMetrics.length > 0) {
    comparisonMetrics.forEach((metric, index) => {
      if (lineChartData[index]) {
        // Add dotted line data to existing chart points
        metrics.forEach((metricName) => {
          lineChartData[index][`dotted_${metricName}`] = parseMetricValue(
            metric[metricName as keyof typeof metric]
          );
        });
      }
    });
  }
  
  // Create cards data with growth calculations
  const cardsData: Record<string, CardsData> = {};
  
  metrics.forEach((metricName) => {
    const primaryValue = parseMetricValue(primaryTotals[metricName as keyof typeof primaryTotals]);
    const comparisonValue = comparisonTotals 
      ? parseMetricValue(comparisonTotals[metricName as keyof typeof comparisonTotals])
      : 0;
    
    cardsData[metricName] = {
      amount: primaryValue,
      growth: comparisonTotals ? calculateGrowth(primaryValue, comparisonValue) : "0%",
    };
    
    // Add dotted line cards data if comparison exists
    if (comparisonTotals) {
      cardsData[`dotted_${metricName}`] = {
        amount: comparisonValue,
        growth: "0%", // Comparison data doesn't show growth
      };
    }
  });
  
  return {
    lineChartData,
    cardsData,
  };
};
