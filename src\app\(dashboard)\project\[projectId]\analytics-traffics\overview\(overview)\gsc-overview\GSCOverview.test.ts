import { processGSCData } from "./GSCOverview.utils";
import {
  MOCK_PRIMARY_DATA,
  MOCK_COMPARISON_DATA,
} from "./GSCOverview.mockData";

// Using comprehensive mock data from GSCOverview.mockData.ts

/**
 * Test function to verify data processing works correctly
 */
export const testGSCDataProcessing = () => {
  console.log("🧪 Testing GSC Data Processing...");

  // Test with primary data only
  const primaryOnlyResult = processGSCData(MOCK_PRIMARY_DATA);
  console.log("📊 Primary data only result:", {
    lineChartDataLength: primaryOnlyResult.lineChartData.length,
    cardsDataKeys: Object.keys(primaryOnlyResult.cardsData),
    firstDataPoint: primaryOnlyResult.lineChartData[0],
    clicksCardData: primaryOnlyResult.cardsData.Clicks,
  });

  // Test with both primary and comparison data
  const dualRangeResult = processGSCData(
    MOCK_PRIMARY_DATA,
    MOCK_COMPARISON_DATA
  );
  console.log("📊 Dual range result:", {
    lineChartDataLength: dualRangeResult.lineChartData.length,
    cardsDataKeys: Object.keys(dualRangeResult.cardsData),
    firstDataPoint: dualRangeResult.lineChartData[0],
    clicksCardData: dualRangeResult.cardsData.Clicks,
    dottedClicksCardData: dualRangeResult.cardsData.dotted_Clicks,
  });

  // Verify dotted lines are present in dual range
  const hasDottedLines = dualRangeResult.lineChartData.some((point) =>
    Object.keys(point).some((key) => key.includes("dotted_"))
  );

  console.log("✅ Test Results:", {
    primaryDataProcessed: primaryOnlyResult.lineChartData.length > 0,
    dualRangeProcessed: dualRangeResult.lineChartData.length > 0,
    dottedLinesPresent: hasDottedLines,
    growthCalculated: dualRangeResult.cardsData.Clicks.growth !== "0%",
  });

  return {
    primaryOnlyResult,
    dualRangeResult,
    testsPassed: {
      primaryDataProcessed: primaryOnlyResult.lineChartData.length > 0,
      dualRangeProcessed: dualRangeResult.lineChartData.length > 0,
      dottedLinesPresent: hasDottedLines,
      growthCalculated: dualRangeResult.cardsData.Clicks.growth !== "0%",
    },
  };
};

// Run test if this file is executed directly
if (typeof window === "undefined") {
  console.log("🚀 Running GSC Data Processing Tests...");
  const results = testGSCDataProcessing();

  console.log("\n📋 Test Summary:");
  console.log(
    "✅ Primary data processed:",
    results.testsPassed.primaryDataProcessed
  );
  console.log(
    "✅ Dual range processed:",
    results.testsPassed.dualRangeProcessed
  );
  console.log(
    "✅ Dotted lines present:",
    results.testsPassed.dottedLinesPresent
  );
  console.log("✅ Growth calculated:", results.testsPassed.growthCalculated);

  const allTestsPassed = Object.values(results.testsPassed).every(Boolean);
  console.log(
    allTestsPassed ? "\n🎉 All tests passed!" : "\n❌ Some tests failed!"
  );
}
