import { processGSCData } from "./GSCOverview.utils";
import { GSCAPIResponse } from "./GSCOverview.types";

// Test data based on the provided API format
const mockPrimaryData: GSCAPIResponse = {
  status: "success",
  project_id: "51e74171-0e23-4f7e-ad4d-7ffae8ffdab1",
  period: {
    start_date: "2025-06-01",
    end_date: "2025-06-30",
    days_count: 30
  },
  data: {
    "web-pages": {
      totals: {
        Clicks: "2",
        CTR: "0.1",
        Search_Visibility: "1498k",
        Impression: "1498k",
        Avg_Position: "63.5"
      },
      daily_metrics: [
        {
          date: "20250601",
          Clicks: "0",
          CTR: "0.0",
          Search_Visibility: "69",
          Impression: "69",
          Avg_Position: "73.9"
        },
        {
          date: "20250602",
          Clicks: "0",
          CTR: "0.0",
          Search_Visibility: "82",
          Impression: "82",
          Avg_Position: "72.7"
        }
      ]
    }
  }
};

const mockComparisonData: GSCAPIResponse = {
  status: "success",
  project_id: "51e74171-0e23-4f7e-ad4d-7ffae8ffdab1",
  period: {
    start_date: "2025-05-01",
    end_date: "2025-05-30",
    days_count: 30
  },
  data: {
    "web-pages": {
      totals: {
        Clicks: "1",
        CTR: "0.05",
        Search_Visibility: "1200k",
        Impression: "1200k",
        Avg_Position: "65.0"
      },
      daily_metrics: [
        {
          date: "20250501",
          Clicks: "0",
          CTR: "0.0",
          Search_Visibility: "50",
          Impression: "50",
          Avg_Position: "70.0"
        },
        {
          date: "20250502",
          Clicks: "1",
          CTR: "1.0",
          Search_Visibility: "60",
          Impression: "60",
          Avg_Position: "68.0"
        }
      ]
    }
  }
};

/**
 * Test function to verify data processing works correctly
 */
export const testGSCDataProcessing = () => {
  console.log("🧪 Testing GSC Data Processing...");
  
  // Test with primary data only
  const primaryOnlyResult = processGSCData(mockPrimaryData);
  console.log("📊 Primary data only result:", {
    lineChartDataLength: primaryOnlyResult.lineChartData.length,
    cardsDataKeys: Object.keys(primaryOnlyResult.cardsData),
    firstDataPoint: primaryOnlyResult.lineChartData[0],
    clicksCardData: primaryOnlyResult.cardsData.Clicks
  });
  
  // Test with both primary and comparison data
  const dualRangeResult = processGSCData(mockPrimaryData, mockComparisonData);
  console.log("📊 Dual range result:", {
    lineChartDataLength: dualRangeResult.lineChartData.length,
    cardsDataKeys: Object.keys(dualRangeResult.cardsData),
    firstDataPoint: dualRangeResult.lineChartData[0],
    clicksCardData: dualRangeResult.cardsData.Clicks,
    dottedClicksCardData: dualRangeResult.cardsData.dotted_Clicks
  });
  
  // Verify dotted lines are present in dual range
  const hasDottedLines = dualRangeResult.lineChartData.some(point => 
    Object.keys(point).some(key => key.includes("dotted_"))
  );
  
  console.log("✅ Test Results:", {
    primaryDataProcessed: primaryOnlyResult.lineChartData.length > 0,
    dualRangeProcessed: dualRangeResult.lineChartData.length > 0,
    dottedLinesPresent: hasDottedLines,
    growthCalculated: dualRangeResult.cardsData.Clicks.growth !== "0%"
  });
  
  return {
    primaryOnlyResult,
    dualRangeResult,
    testsPassed: {
      primaryDataProcessed: primaryOnlyResult.lineChartData.length > 0,
      dualRangeProcessed: dualRangeResult.lineChartData.length > 0,
      dottedLinesPresent: hasDottedLines,
      growthCalculated: dualRangeResult.cardsData.Clicks.growth !== "0%"
    }
  };
};

// Run test if this file is executed directly
if (typeof window === "undefined") {
  testGSCDataProcessing();
}
